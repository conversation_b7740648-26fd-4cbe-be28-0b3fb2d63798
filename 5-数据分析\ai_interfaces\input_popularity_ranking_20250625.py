#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
录入2025年6月25日晚上人气排名数据
基于用户提供的截图数据
"""

import sqlite3
from datetime import datetime
from pathlib import Path

DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def input_popularity_ranking_data():
    """录入人气排名数据"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        # 人气排名数据（基于截图）
        popularity_data = [
            (1, '浩德股份', '+9.94%', 4309.7, '7天7板 PE概念 PCB概念'),
            (2, '东方财富', '+10.04%', 4146.7, '互联网保险 互联网金融'),
            (3, '恒宝股份', '-2.63%', 3634.2, '持续上涨 数字货币 电子身份证'),
            (4, '国轩高科', '+8.16%', 3145.4, '2天1板 固态电池 钠离子电池'),
            (5, '安妮股份', '-3.98%', 2683.4, '持续上涨 数权金融概念 Web3.0'),
            (6, '海联金汇', '+9.98%', 2511.7, '3天3板 数字货币 移动支付'),
            (7, '中京电子', '-5.77%', 2352.9, '6天4板 PCB概念 MiniLED'),
            (8, '华之杰', '+57.84%', 2344.8, '注册制次新股 人民币贬值概念'),
            (9, '金龙羽', '+3.62%', 2298.8, '3天2板 固态电池 锂电池概念'),
            (10, '长城军工', '+10.00%', 2246.8, '6天5板 兵器重组概念 军民融合'),
            (11, '中毅达', '-2.55%', 2165.7, '西部大开发 国企改革'),
            (12, '四方精创', '+1.11%', 2096.9, '数字货币 跨境支付(CIPS)'),
            (13, '银之杰', '+15.92%', 1808.1, '互联网金融 数字货币'),
            (14, '天风证券', '+10.02%', 1747.2, '首板涨停 期货概念 物业管理'),
            (15, '拉卡拉', '+5.02%', 1685.9, '数字货币 跨境支付(CIPS)'),
            (16, '翠微股份', '+10.03%', 1654.6, '首板涨停 数字货币 移动支付'),
            (17, '同花顺', '+14.49%', 1607.4, '互联网金融 AI概念'),
            (18, '楚天龙', '-6.31%', 1536.6, 'ETC 电子身份证'),
            (19, '东信和平', '-4.61%', 1505.4, '10天5板 电子身份证 数字货币'),
            (20, '融发核电', '+0.12%', 1419.4, '可控核聚变 核电'),
            (21, '兴业股份', '+9.99%', 1397.7, '5天5板 光刻胶 先进封装'),
            (22, '山东墨龙', '-8.78%', 1377.0, '9天7板 页岩气 天然气'),
            (23, '指南针', '+20.00%', 1352.5, '首板涨停 互联网金融 参股券商'),
            (24, '英联股份', '+10.00%', 1321.1, '4天4板 PE概念 固态电池'),
            (25, '湘潭电化', '+10.02%', 1302.3, '5天5板 钠离子电池 固态电池'),
            (26, '新恒汇', '+16.18%', 1266.3, '注册制次新股 芯片概念'),
            (27, '云内动力', '+1.77%', 1191.9, '2天1板 移动支付 无人驾驶'),
            (28, '泰和科技', '+19.98%', 1184.4, '3天3板 钠离子电池 固态电池'),
            (29, '湘财股份', '+10.01%', 1162.2, '2天2板 黑龙江自贸区 参股券商'),
            (30, '浙江世宝', '+9.97%', 1142.2, '2天2板 长安汽车概念 小米汽车'),
            (31, '新国都', '+14.42%', 1132.2, '数字货币 跨境支付(CIPS)'),
            (32, '永安药业', '-7.87%', 1111.7, '宠物经济 维生素'),
            (33, '联化科技', '-9.94%', 1108.0, 'CRO概念 创新药'),
            (34, '汇金股份', '+20.02%', 1047.1, '首板涨停 信创 无人零售'),
            (35, '御银股份', '+6.17%', 1016.9, '互联网金融 区块链'),
            (36, '龙蟠科技', '+10.03%', 1010.1, '2天2板 钠离子电池 固态电池'),
            (37, '赛伍技术', '+3.68%', 999.5, '4天2板 钠离子电池 BC电池'),
            (38, '中科金财', '+5.10%', 978.6, '数字货币 移动支付'),
            (39, '吉大正元', '+10.01%', 978.2, '3天3板 电子身份证 数权金融概念'),
            (40, '慧时胜', '+10.19%', 971.4, '数字货币 华为概念'),
            (41, '中兵红箭', '+10.02%', 969.9, '首板涨停 军民融合 商业航天'),
            (42, '台基股份', '+20.00%', 964.4, '首板涨停 第三代半导体 芯片概念'),
            (43, '保税科技', '+3.69%', 947.9, '4天2板 统一大市场 航运概念'),
            (44, '国泰海通', '+9.09%', 917.5, '期货概念 2025一季报预增'),
            (45, '湖南天雁', '+10.00%', 914.5, '3天2板 兵器重组概念 长安汽车概念'),
            (46, '国盛金控', '+10.03%', 913.1, '2天2板 期货概念 区块链'),
            (47, '建设工业', '+9.99%', 882.1, '首板涨停 兵器重组概念 军民融合'),
            (48, '杭齿前进', '+7.81%', 853.4, '2天1板 减速器 人形机器人'),
            (49, '恒生电子', '+7.34%', 844.4, '数权金融概念 数字货币'),
            (50, '京北方', '+10.03%', 841.0, '首板涨停 数字货币 跨境支付(CIPS)')
        ]
        
        # 录入数据
        for rank, stock_name, change_pct, popularity, concept in popularity_data:
            cursor.execute('''
                INSERT INTO popularity_ranking 
                (日期, 时间点, 排名, 股票名称, 涨跌幅, 热度值, 板数信息, 录入时间)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                '2025-06-25',
                '晚上收盘后',
                rank,
                stock_name,
                change_pct,
                popularity,
                concept,
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))
        
        conn.commit()
        print("✅ 人气排名数据录入成功")
        
        # 验证录入结果
        cursor.execute("SELECT COUNT(*) FROM popularity_ranking WHERE 日期='2025-06-25'")
        count = cursor.fetchone()[0]
        print(f"📊 录入记录数: {count}条")
        
        # 显示前10名
        cursor.execute('''
            SELECT 排名, 股票名称, 涨跌幅, 热度值
            FROM popularity_ranking 
            WHERE 日期='2025-06-25' 
            ORDER BY 排名 
            LIMIT 10
        ''')
        top_10 = cursor.fetchall()
        
        print("\n🔥 人气排名前10:")
        for rank, stock, change, popularity in top_10:
            print(f"  {rank}. {stock} {change} ({popularity}万热度)")
        
        # 统计涨跌分布
        cursor.execute('''
            SELECT 
                SUM(CASE WHEN 涨跌幅 LIKE '+%' THEN 1 ELSE 0 END) as 上涨数,
                SUM(CASE WHEN 涨跌幅 LIKE '-%' THEN 1 ELSE 0 END) as 下跌数
            FROM popularity_ranking 
            WHERE 日期='2025-06-25'
        ''')
        up_down = cursor.fetchone()
        print(f"\n📈 涨跌分布: 上涨{up_down[0]}只, 下跌{up_down[1]}只")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 人气排名数据录入失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 开始录入2025年6月25日晚上人气排名数据...")
    print("📊 基于用户提供的截图数据，共50只股票")
    print("=" * 50)
    
    if input_popularity_ranking_data():
        print("\n🎉 人气排名数据录入完成！")
    else:
        print("\n❌ 人气排名数据录入失败！") 