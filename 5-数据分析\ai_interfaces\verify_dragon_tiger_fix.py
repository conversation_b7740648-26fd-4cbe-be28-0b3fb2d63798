#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
验证龙虎榜修正结果
"""

import sqlite3
from pathlib import Path

# 数据库路径
DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def verify_dragon_tiger_fix():
    """验证龙虎榜修正结果"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        # 查询修正后的数据
        cursor.execute("""
            SELECT date, trader_name, stock_code, stock_name, 
                   buy_amount, sell_amount, net_amount, operation_type, market_signal
            FROM dragon_tiger_list 
            WHERE date='2025-06-25'
            ORDER BY trader_name, stock_name
        """)
        
        results = cursor.fetchall()
        
        print("📊 2025年6月25日龙虎榜数据验证结果")
        print("=" * 50)
        print(f"总记录数: {len(results)}条\n")
        
        if results:
            for row in results:
                date, trader, code, stock, buy, sell, net, op_type, signal = row
                print(f"🎯 {trader}")
                print(f"   股票: {stock} ({code})")
                print(f"   买入: {buy}万")
                print(f"   卖出: {sell}万") 
                print(f"   净额: {net}万 ({op_type})")
                print(f"   信号: {signal}")
                print("-" * 30)
        else:
            print("⚠️ 未找到数据")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    verify_dragon_tiger_fix() 