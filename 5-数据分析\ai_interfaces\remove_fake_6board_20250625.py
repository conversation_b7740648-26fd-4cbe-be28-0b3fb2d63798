#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
删除虚拟的6板数据
保持梯队结构的真实性
"""

import sqlite3
from pathlib import Path

DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def remove_fake_6board():
    """删除虚拟的6板数据"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        # 删除虚拟的6板数据
        cursor.execute('''
            DELETE FROM space_ladder 
            WHERE date='2025-06-25' 
            AND stock_name='无6板个股' 
            AND board_level='6板'
        ''')
        
        deleted_count = cursor.rowcount
        conn.commit()
        
        if deleted_count > 0:
            print(f"✅ 已删除 {deleted_count} 条虚拟6板数据")
        else:
            print("ℹ️ 没有找到虚拟6板数据")
        
        # 显示当前真实的梯队结构
        cursor.execute('''
            SELECT board_level, COUNT(*) as count, GROUP_CONCAT(stock_name) as stocks
            FROM space_ladder 
            WHERE date='2025-06-25' 
            AND board_level NOT LIKE '%反包%'
            AND board_level REGEXP '^[0-9]+板$|^[一二三四五六七八九十]+板$'
            GROUP BY board_level 
            ORDER BY CAST(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(board_level, '一', '1'), '二', '2'), '三', '3'), '四', '4'), '五', '5'), '六', '6'), '七', '7'), '八', '8'), '九', '9'), '板', '') AS INTEGER)
        ''')
        
        real_structure = cursor.fetchall()
        
        print("\n📊 真实梯队结构:")
        for level, count, stocks in real_structure:
            print(f"  {level}: {count}只 ({stocks})")
        
        # 分析梯队完整性
        board_numbers = []
        for level, _, _ in real_structure:
            # 处理中文数字
            level_clean = level.replace('板', '')
            if level_clean in ['一', '1']:
                board_numbers.append(1)
            elif level_clean in ['二', '2']:
                board_numbers.append(2)
            elif level_clean in ['三', '3']:
                board_numbers.append(3)
            elif level_clean in ['四', '4']:
                board_numbers.append(4)
            elif level_clean in ['五', '5']:
                board_numbers.append(5)
            elif level_clean in ['六', '6']:
                board_numbers.append(6)
            elif level_clean in ['七', '7']:
                board_numbers.append(7)
        
        board_numbers = sorted(set(board_numbers))
        print(f"\n📈 实际存在板数: {board_numbers}")
        
        # 检查连续性
        missing_boards = []
        if board_numbers:
            for i in range(1, max(board_numbers) + 1):
                if i not in board_numbers:
                    missing_boards.append(i)
        
        if missing_boards:
            print(f"⚠️ 缺失板数: {missing_boards}")
            print("💡 这是真实市场情况，不需要修补")
        else:
            print("✅ 梯队结构完整连续")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 删除虚拟数据失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 删除虚拟6板数据，保持梯队结构真实性...")
    print("=" * 50)
    
    if remove_fake_6board():
        print("\n🎉 梯队结构已恢复真实状态！")
    else:
        print("\n❌ 操作失败！") 