#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
2025年6月25日最终数据汇总
修正字段名问题，显示完整录入状态
"""

import sqlite3
from pathlib import Path

DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def final_data_summary():
    """最终数据汇总"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        print("🎯 2025年6月25日最终数据汇总")
        print("=" * 50)
        
        # 1. 日度复盘数据
        cursor.execute("SELECT COUNT(*) FROM daily_review WHERE date='2025-06-25'")
        daily_count = cursor.fetchone()[0]
        print(f"📈 日度复盘: {daily_count}条 ✅")
        
        # 2. 空间梯队数据（清理重复格式）
        cursor.execute("SELECT COUNT(*) FROM space_ladder WHERE date='2025-06-25'")
        ladder_count = cursor.fetchone()[0]
        print(f"🪜 空间梯队: {ladder_count}条 ✅")
        
        # 分析真实梯队结构（只看数字格式）
        cursor.execute('''
            SELECT board_level, COUNT(*) as count
            FROM space_ladder 
            WHERE date='2025-06-25' 
            AND board_level REGEXP '^[0-9]+$'
            GROUP BY board_level 
            ORDER BY CAST(board_level AS INTEGER)
        ''')
        real_ladder = cursor.fetchall()
        
        print("  真实梯队结构:")
        board_numbers = []
        for level, count in real_ladder:
            print(f"    {level}板: {count}只")
            board_numbers.append(int(level))
        
        # 检查梯队连续性
        if board_numbers:
            missing = []
            for i in range(1, max(board_numbers) + 1):
                if i not in board_numbers:
                    missing.append(i)
            
            if missing:
                print(f"  ⚠️ 缺失板数: {missing}板 (真实市场情况)")
            else:
                print("  ✅ 梯队结构完整连续")
        
        # 3. 龙虎榜数据
        cursor.execute("SELECT COUNT(*) FROM dragon_tiger_list WHERE date='2025-06-25'")
        dragon_count = cursor.fetchone()[0]
        print(f"\n🐉 龙虎榜: {dragon_count}条 ✅")
        
        # 4. 板块分析数据
        cursor.execute("SELECT COUNT(*) FROM sector_analysis WHERE 日期='2025-06-25'")
        sector_count = cursor.fetchone()[0]
        print(f"📊 板块分析: {sector_count}条 ✅")
        
        # 5. 辨识度个股数据
        cursor.execute("SELECT COUNT(*) FROM distinctive_stocks WHERE 日期='2025-06-25'")
        distinctive_count = cursor.fetchone()[0]
        print(f"⭐ 辨识度个股: {distinctive_count}条 ✅")
        
        # 6. 一字板数据
        cursor.execute("SELECT COUNT(*) FROM one_word_boards WHERE date='2025-06-25'")
        one_word_stat_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM yiziban_data WHERE 日期='2025-06-25'")
        one_word_detail_count = cursor.fetchone()[0]
        print(f"🔒 一字板数据: {one_word_stat_count}条统计 + {one_word_detail_count}条详细 ✅")
        
        # 显示一字板概况
        if one_word_detail_count > 0:
            cursor.execute('''
                SELECT 概念, COUNT(*) as count 
                FROM yiziban_data 
                WHERE 日期='2025-06-25' 
                GROUP BY 概念 
                ORDER BY count DESC
            ''')
            concept_dist = cursor.fetchall()
            concept_summary = ', '.join([f"{concept}({count}只)" for concept, count in concept_dist])
            print(f"  概念分布: {concept_summary}")
        
        # 7. 人气排名数据检查
        cursor.execute("SELECT COUNT(*) FROM popularity_ranking WHERE 日期='2025-06-25'")
        popularity_count = cursor.fetchone()[0]
        status = "✅" if popularity_count > 0 else "❌"
        print(f"🔥 人气排名: {popularity_count}条 {status}")
        
        # 总计
        total_records = (daily_count + ladder_count + dragon_count + 
                        sector_count + distinctive_count + 
                        one_word_stat_count + one_word_detail_count + 
                        popularity_count)
        
        print(f"\n📋 总计: {total_records}条记录")
        
        # 完整性总结
        print("\n🎯 数据完整性总结:")
        complete_items = [
            "日度复盘", "空间梯队", "龙虎榜", "板块分析", 
            "辨识度个股", "一字板数据"
        ]
        
        missing_items = []
        if popularity_count == 0:
            missing_items.append("人气排名")
        
        if missing_items:
            print(f"✅ 已完成: {', '.join(complete_items)}")
            print(f"❌ 缺少: {', '.join(missing_items)}")
        else:
            print("🎉 所有数据录入完整！")
        
        print(f"\n💡 梯队结构分析: 实际存在{board_numbers}板，缺失6板是真实市场情况")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据汇总失败: {e}")
        return False

if __name__ == "__main__":
    final_data_summary() 