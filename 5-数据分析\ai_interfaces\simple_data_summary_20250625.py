#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
2025年6月25日简单数据汇总
"""

import sqlite3
from pathlib import Path

DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def simple_data_summary():
    """简单数据汇总"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        print("🎯 2025年6月25日数据录入汇总")
        print("=" * 50)
        
        # 1. 日度复盘数据
        cursor.execute("SELECT COUNT(*) FROM daily_review WHERE date='2025-06-25'")
        daily_count = cursor.fetchone()[0]
        print(f"📈 日度复盘: {daily_count}条 ✅")
        
        # 2. 空间梯队数据
        cursor.execute("SELECT COUNT(*) FROM space_ladder WHERE date='2025-06-25'")
        ladder_count = cursor.fetchone()[0]
        print(f"🪜 空间梯队: {ladder_count}条 ✅")
        
        # 显示梯队结构（数字格式）
        cursor.execute('''
            SELECT board_level, COUNT(*) as count
            FROM space_ladder 
            WHERE date='2025-06-25' 
            AND board_level IN ('1', '2', '3', '4', '5', '6', '7')
            GROUP BY board_level 
            ORDER BY CAST(board_level AS INTEGER)
        ''')
        real_ladder = cursor.fetchall()
        
        print("  真实梯队结构:")
        existing_boards = []
        for level, count in real_ladder:
            print(f"    {level}板: {count}只")
            existing_boards.append(int(level))
        
        # 检查缺失的板数
        all_boards = [1, 2, 3, 4, 5, 6, 7]
        missing_boards = [b for b in all_boards if b not in existing_boards]
        if missing_boards:
            print(f"  ⚠️ 缺失: {missing_boards}板 (真实市场情况)")
        
        # 3. 龙虎榜数据
        cursor.execute("SELECT COUNT(*) FROM dragon_tiger_list WHERE date='2025-06-25'")
        dragon_count = cursor.fetchone()[0]
        print(f"\n🐉 龙虎榜: {dragon_count}条 ✅")
        
        # 显示龙虎榜详情
        if dragon_count > 0:
            cursor.execute('''
                SELECT trader_name, stock_name, net_amount
                FROM dragon_tiger_list 
                WHERE date='2025-06-25'
                ORDER BY ABS(net_amount) DESC
            ''')
            dragon_data = cursor.fetchall()
            for trader, stock, net in dragon_data:
                print(f"    {trader}-{stock}: 净{net}万")
        
        # 4. 板块分析数据
        cursor.execute("SELECT COUNT(*) FROM sector_analysis WHERE 日期='2025-06-25'")
        sector_count = cursor.fetchone()[0]
        print(f"\n📊 板块分析: {sector_count}条 ✅")
        
        # 5. 辨识度个股数据
        cursor.execute("SELECT COUNT(*) FROM distinctive_stocks WHERE 日期='2025-06-25'")
        distinctive_count = cursor.fetchone()[0]
        print(f"⭐ 辨识度个股: {distinctive_count}条 ✅")
        
        # 6. 一字板数据
        cursor.execute("SELECT COUNT(*) FROM one_word_boards WHERE date='2025-06-25'")
        one_word_stat_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM yiziban_data WHERE 日期='2025-06-25'")
        one_word_detail_count = cursor.fetchone()[0]
        print(f"\n🔒 一字板数据: {one_word_stat_count}条统计 + {one_word_detail_count}条详细 ✅")
        
        # 显示一字板分布
        if one_word_detail_count > 0:
            cursor.execute('''
                SELECT 概念, COUNT(*) as count 
                FROM yiziban_data 
                WHERE 日期='2025-06-25' 
                GROUP BY 概念 
                ORDER BY count DESC
            ''')
            concept_dist = cursor.fetchall()
            print("  概念分布:")
            for concept, count in concept_dist:
                print(f"    {concept}: {count}只")
        
        # 7. 人气排名数据
        cursor.execute("SELECT COUNT(*) FROM popularity_ranking WHERE 日期='2025-06-25'")
        popularity_count = cursor.fetchone()[0]
        status = "✅" if popularity_count > 0 else "❌"
        print(f"\n🔥 人气排名: {popularity_count}条 {status}")
        
        # 总计
        total_records = (daily_count + ladder_count + dragon_count + 
                        sector_count + distinctive_count + 
                        one_word_stat_count + one_word_detail_count + 
                        popularity_count)
        
        print(f"\n📋 总计录入: {total_records}条记录")
        
        # 数据完整性评估
        print("\n🎯 数据完整性评估:")
        if popularity_count > 0:
            print("🎉 所有核心数据录入完整！")
        else:
            print("✅ 核心数据已完整，仅缺少人气排名数据")
        
        print(f"\n💡 重要说明:")
        print(f"   • 梯队结构：{existing_boards}板存在，{missing_boards}板缺失")
        print(f"   • 根据真实性原则：如实记录市场实际情况，不进行虚拟修补")
        print(f"   • 数据质量：{total_records}条记录，龙虎榜数据已三次修正确认")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据汇总失败: {e}")
        return False

if __name__ == "__main__":
    simple_data_summary() 