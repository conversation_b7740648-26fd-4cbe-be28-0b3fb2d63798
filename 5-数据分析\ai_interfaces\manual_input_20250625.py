#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
2025年6月25日复盘数据手动录入脚本
基于用户提供的截图和文字数据进行完整录入
"""

import sys
import sqlite3
from datetime import datetime
from pathlib import Path

# 添加核心模块路径
current_dir = Path(__file__).parent
ai_backend_path = current_dir.parent / "ai_backend"
sys.path.append(str(ai_backend_path / "core_modules"))

# 数据库路径
DB_PATH = current_dir.parent / "databases" / "trading_data.db"

def create_connection():
    """创建数据库连接"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        return conn
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def insert_daily_review():
    """录入日度复盘数据"""
    conn = create_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # 日度复盘主表数据
        daily_data = {
            'date': '2025-06-25',
            'market_close_index': 3455.97,  # 沪指收盘
            'market_change': 35.41,  # 涨跌点数
            'market_change_pct': 1.04,  # 涨跌幅%
            'total_volume': 16027,  # 成交量(亿)
            'limit_up_count': 66,  # 涨停家数(实际涨停)
            'limit_down_count': 2,  # 跌停家数(实际跌停)
            'break_board_rate': 18.52,  # 破板率%
            'continuous_board_rate': 22,  # 连板率%(二板)
            'market_sentiment': 75,  # 综合强度
            'market_summary': '市场保持走强，大金融放量走强，带动指数向上突破。两市成交1.6万亿，市场人气快速回暖。',
            'pressure_level': 3461.5,  # 压力位
            'support_level': None,  # 支撑位
            'trend_judgment': '指数价涨量增，量价正常。权重大金融开始带动走强，市场有机会从横盘震荡转为向上的趋势',
            'kdj_status': '金手指区间，指数走出三连阳',
            'institution_activity': '机构交易35笔，净买入20笔，3笔过亿买入',
            'operation_strategy': '对后市仍可保持信心，耐心等待市场情绪的趋势修复，可更乐观一点'
        }
        
        # 插入日度复盘数据
        columns = ', '.join(daily_data.keys())
        placeholders = ', '.join(['?' for _ in daily_data])
        
        cursor.execute(f'''
            INSERT OR REPLACE INTO daily_review ({columns})
            VALUES ({placeholders})
        ''', list(daily_data.values()))
        
        print(f"✅ 日度复盘数据录入成功: {daily_data['date']}")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ 日度复盘数据录入失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def insert_board_analysis():
    """录入连板数据分析"""
    conn = create_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # 连板统计数据
        board_data = [
            ('2025-06-25', '一板', 45, 22),  # 连板率22%中
            ('2025-06-25', '二板', 13, 50),  # 连板率50%高
            ('2025-06-25', '三板', 3, 63),   # 连板率63%高
            ('2025-06-25', '高度板', 5, None)  # 高度板5家
        ]
        
        for date, board_level, count, success_rate in board_data:
            cursor.execute('''
                INSERT OR REPLACE INTO board_analysis 
                (date, board_level, stock_count, success_rate, notes)
                VALUES (?, ?, ?, ?, ?)
            ''', (date, board_level, count, success_rate, '题材存在炒作机会'))
        
        print("✅ 连板数据分析录入成功")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ 连板数据分析录入失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def insert_space_ladder():
    """录入空间梯队数据"""
    conn = create_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # 空间梯队数据(基于截图中的市场梯队信息)
        ladder_data = [
            # 七板
            ('2025-06-25', '七板', '诺德股份', '600110', '固态电池', None),
            
            # 五板  
            ('2025-06-25', '五板', '湘潭电化', '002125', '固态电池', None),
            ('2025-06-25', '五板', '兴业股份', '603928', '芯片', None),
            
            # 四板
            ('2025-06-25', '四板', '英联股份', '002846', '光伏', None),
            ('2025-06-25', '四板', '新通联', '603022', '光伏', None),
            
            # 三板
            ('2025-06-25', '三板', '吉大正元', '003029', '金融概念', None),
            ('2025-06-25', '三板', '海联金汇', '002537', '金融概念', None),
            ('2025-06-25', '三板', '泰和科技', '300801', '芯片', None),
            
            # 二板
            ('2025-06-25', '二板', '天际股份', '002759', '固态电池', None),
            ('2025-06-25', '二板', '大东南', '002263', '固态电池', None),
            ('2025-06-25', '二板', '龙蟠科技', '603906', '固态电池', None),
            
            # 首板
            ('2025-06-25', '一板', '中光学', '002189', '军工', None),
            ('2025-06-25', '一板', '北方导航', '600435', '军工', None),
            ('2025-06-25', '一板', '建设工业', '603948', '军工', None),
            
            # 反包
            ('2025-06-25', '反包', '长城军工', '601606', '军工', '6天5板'),
            ('2025-06-25', '反包', '万里马', '300591', '军工', '3天2板'),
        ]
        
        for date, board_level, stock_name, stock_code, concept, ladder_position in ladder_data:
            cursor.execute('''
                INSERT OR REPLACE INTO space_ladder 
                (date, board_level, stock_name, stock_code, concept, ladder_position)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (date, board_level, stock_name, stock_code, concept, ladder_position))
        
        print("✅ 空间梯队数据录入成功")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ 空间梯队数据录入失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def insert_sector_analysis():
    """录入板块分析数据"""
    conn = create_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # 板块涨跌数据
        sector_data = [
            # 涨幅板块
            ('2025-06-25', '兵装重组概念', 8.60, '华强科技'),
            ('2025-06-25', '多元金融', 5.54, '习微股份'),
            ('2025-06-25', '军工装备', 4.74, '国瑞科技'),
            ('2025-06-25', '证券', 4.54, '东方财富'),
            ('2025-06-25', '互联网保险', 4.17, '天利科技'),
            
            # 跌幅板块
            ('2025-06-25', '油气开采及服务', -2.59, '海油发展'),
            ('2025-06-25', '可燃冰', -1.25, '神开股份'),
            ('2025-06-25', '农化制品', -0.92, '湖南海利'),
            ('2025-06-25', '石油加工贸易', -0.87, '恒通股份'),
            ('2025-06-25', '港口航运', -0.83, '海峡股份'),
        ]
        
        for date, sector_name, change_pct, leading_stock in sector_data:
            cursor.execute('''
                INSERT OR REPLACE INTO sector_analysis 
                (date, sector_name, change_pct, leading_stock, notes)
                VALUES (?, ?, ?, ?, ?)
            ''', (date, sector_name, change_pct, leading_stock, '权重方向走强带动市场'))
        
        print("✅ 板块分析数据录入成功")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ 板块分析数据录入失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def insert_dragon_tiger_list():
    """录入龙虎榜数据"""
    conn = create_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # 龙虎榜数据(基于截图信息)
        dragon_tiger_data = [
            ('2025-06-25', '湘潭电化', '002125', '炒股养家', 5679, -9833, '5连板'),
            ('2025-06-25', '湘潭电化', '002125', '上塘路', 1427, -2942, '5连板'),
            ('2025-06-25', '怕诚股份', '601133', '上塘路', 1427, -2942, '首板'),
        ]
        
        for date, stock_name, stock_code, seat_name, buy_amount, sell_amount, notes in dragon_tiger_data:
            cursor.execute('''
                INSERT OR REPLACE INTO dragon_tiger_list 
                (date, stock_name, stock_code, seat_name, buy_amount, sell_amount, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (date, stock_name, stock_code, seat_name, buy_amount, sell_amount, notes))
        
        print("✅ 龙虎榜数据录入成功")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ 龙虎榜数据录入失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def main():
    """主执行函数"""
    print("🚀 开始录入2025年6月25日复盘数据...")
    
    success_count = 0
    
    # 录入各类数据
    if insert_daily_review():
        success_count += 1
    
    if insert_board_analysis():
        success_count += 1
        
    if insert_space_ladder():
        success_count += 1
        
    if insert_sector_analysis():
        success_count += 1
        
    if insert_dragon_tiger_list():
        success_count += 1
    
    print(f"\n📊 数据录入完成！成功录入 {success_count}/5 个数据模块")
    print("✅ 2025年6月25日复盘数据已完整录入到本地数据库")

if __name__ == "__main__":
    main() 