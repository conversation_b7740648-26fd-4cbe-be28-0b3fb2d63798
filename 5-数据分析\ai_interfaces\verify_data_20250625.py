#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
验证2025年6月25日数据录入结果
"""

import sqlite3
from pathlib import Path

# 数据库路径
DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def verify_data():
    """验证数据录入结果"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        print("🔍 验证2025年6月25日数据录入结果")
        print("=" * 50)
        
        # 检查日度复盘数据
        cursor.execute("SELECT * FROM daily_review WHERE date='2025-06-25'")
        daily_data = cursor.fetchall()
        print(f"📊 日度复盘数据: {len(daily_data)}条")
        if daily_data:
            print(f"   指数收盘: {daily_data[0][2]}, 综合强度: {daily_data[0][11]}")
        
        # 检查空间梯队数据
        cursor.execute("SELECT * FROM space_ladder WHERE date='2025-06-25'")
        ladder_data = cursor.fetchall()
        print(f"🎯 空间梯队数据: {len(ladder_data)}条")
        if ladder_data:
            board_levels = [row[2] for row in ladder_data if row[2] is not None and isinstance(row[2], int)]
            if board_levels:
                print(f"   最高板: {max(board_levels)}板")
        
        # 检查板块分析数据
        cursor.execute("SELECT * FROM sector_analysis WHERE date='2025-06-25'")
        sector_data = cursor.fetchall()
        print(f"📈 板块分析数据: {len(sector_data)}条")
        
        # 检查龙虎榜数据
        cursor.execute("SELECT * FROM dragon_tiger_list WHERE date='2025-06-25'")
        dragon_data = cursor.fetchall()
        print(f"🐉 龙虎榜数据: {len(dragon_data)}条")
        
        # 检查辨识度个股数据
        cursor.execute("SELECT * FROM distinctive_stocks WHERE date='2025-06-25'")
        distinctive_data = cursor.fetchall()
        print(f"⭐ 辨识度个股数据: {len(distinctive_data)}条")
        
        print("\n✅ 数据录入验证完成")
        
        # 显示空间梯队详情
        if ladder_data:
            print("\n🎯 空间梯队详情:")
            sorted_data = sorted(ladder_data, key=lambda x: x[2] if x[2] is not None else 0, reverse=True)
            for row in sorted_data:
                print(f"   {row[2]}板: {row[4]} ({row[3]}) - {row[5]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据验证失败: {e}")

if __name__ == "__main__":
    verify_data() 