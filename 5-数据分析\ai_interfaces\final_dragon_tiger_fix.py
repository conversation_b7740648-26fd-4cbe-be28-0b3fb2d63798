#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
最终修正龙虎榜数据 - 修正卖出金额和股票名称
"""

import sqlite3
from datetime import datetime
from pathlib import Path

DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def final_fix_dragon_tiger():
    """最终修正龙虎榜数据"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        # 删除错误数据
        cursor.execute("DELETE FROM dragon_tiger_list WHERE date='2025-06-25'")
        print("🗑️ 已删除错误数据")
        
        # 基于用户最新确认的正确数据
        correct_data = [
            # 炒股养家 - 湘潭电化 (买入5679万，卖出98.33万，净买入5580.67万)
            ('2025-06-25', '炒股养家', '002125', '湘潭电化', 5679, 98.33, 5580.67, '净买入', '5连板继续加仓'),
            
            # 上塘路 - 湘潭电化 (买入1427万，卖出2942万，净卖出1515万)  
            ('2025-06-25', '上塘路', '002125', '湘潭电化', 1427, 2942, -1515, '净卖出', '5连板获利了结'),
            
            # 上塘路 - 柏诚股份 (买入1427万，卖出2942万，净卖出1515万)
            ('2025-06-25', '上塘路', '603303', '柏诚股份', 1427, 2942, -1515, '净卖出', '首板参与后离场'),
        ]
        
        for data in correct_data:
            cursor.execute('''
                INSERT INTO dragon_tiger_list 
                (date, trader_name, stock_code, stock_name, buy_amount, sell_amount, net_amount, operation_type, market_signal, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (*data, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        conn.commit()
        print("✅ 龙虎榜数据最终修正完成")
        
        # 验证结果
        cursor.execute("SELECT trader_name, stock_name, buy_amount, sell_amount, net_amount, operation_type FROM dragon_tiger_list WHERE date='2025-06-25'")
        results = cursor.fetchall()
        
        print("\n📊 最终修正后的龙虎榜数据:")
        for row in results:
            trader, stock, buy, sell, net, op_type = row
            print(f"  {trader}: {stock}")
            print(f"    买入: {buy}万, 卖出: {sell}万")
            print(f"    净额: {net}万 ({op_type})")
            print("-" * 30)
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修正失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 最终修正2025年6月25日龙虎榜数据...")
    print("📊 修正炒股养家卖出金额和股票名称")
    print("=" * 50)
    
    if final_fix_dragon_tiger():
        print("\n🎉 龙虎榜数据最终修正完成！")
    else:
        print("\n❌ 龙虎榜数据修正失败！") 