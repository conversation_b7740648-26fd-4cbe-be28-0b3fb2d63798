#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
检查数据库结构脚本
"""

import sqlite3
from pathlib import Path

# 数据库路径
DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def check_database_structure():
    """检查数据库结构"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"📊 数据库路径: {DB_PATH}")
        print(f"📋 数据库中的表: {len(tables)}个")
        print("-" * 50)
        
        for table in tables:
            print(f"\n📄 表名: {table}")
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            print("  列信息:")
            for col in columns:
                print(f"    {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")

if __name__ == "__main__":
    check_database_structure() 