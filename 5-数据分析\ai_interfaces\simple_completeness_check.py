#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-

import sqlite3
from pathlib import Path

DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

conn = sqlite3.connect(str(DB_PATH))
cursor = conn.cursor()

print("📊 2025年6月25日复盘数据完整性检查")
print("=" * 50)

# 检查各表数据
print("✅ 已录入数据:")
cursor.execute("SELECT COUNT(*) FROM daily_review WHERE date='2025-06-25'")
print(f"  日度复盘: {cursor.fetchone()[0]}条")

cursor.execute("SELECT COUNT(*) FROM space_ladder WHERE date='2025-06-25'")
print(f"  空间梯队: {cursor.fetchone()[0]}条")

cursor.execute("SELECT COUNT(*) FROM sector_analysis WHERE date='2025-06-25'")
print(f"  板块分析: {cursor.fetchone()[0]}条")

cursor.execute("SELECT COUNT(*) FROM dragon_tiger_list WHERE date='2025-06-25'")
print(f"  龙虎榜: {cursor.fetchone()[0]}条")

cursor.execute("SELECT COUNT(*) FROM distinctive_stocks WHERE date='2025-06-25'")
print(f"  辨识度个股: {cursor.fetchone()[0]}条")

cursor.execute("SELECT COUNT(*) FROM one_word_boards WHERE date='2025-06-25'")
one_word_count = cursor.fetchone()[0]
print(f"  一字板统计: {one_word_count}条")

try:
    cursor.execute("SELECT COUNT(*) FROM yiziban_data WHERE 日期='2025-06-25'")
    yiziban_count = cursor.fetchone()[0]
    print(f"  一字板详细: {yiziban_count}条")
except:
    print(f"  一字板详细: 0条")

try:
    cursor.execute("SELECT COUNT(*) FROM popularity_ranking WHERE 日期='2025-06-25'")
    popularity_count = cursor.fetchone()[0]
    print(f"  人气排名: {popularity_count}条")
except:
    print(f"  人气排名: 0条")

print("\n❌ 缺少的数据:")
if one_word_count == 0:
    print("  - 一字板统计数据（今日一字板总数和分布）")

print("  - 一字板详细数据（具体一字板股票清单）")
print("  - 人气排名数据（今日人气股排名）")

conn.close() 