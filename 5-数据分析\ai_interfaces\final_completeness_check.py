#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-

import sqlite3
from pathlib import Path

DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

conn = sqlite3.connect(str(DB_PATH))
cursor = conn.cursor()

print("2025年6月25日复盘数据完整性检查")
print("=" * 40)

print("已录入数据:")
cursor.execute("SELECT COUNT(*) FROM daily_review WHERE date='2025-06-25'")
print(f"日度复盘: {cursor.fetchone()[0]}条")

cursor.execute("SELECT COUNT(*) FROM space_ladder WHERE date='2025-06-25'")
print(f"空间梯队: {cursor.fetchone()[0]}条")

cursor.execute("SELECT COUNT(*) FROM dragon_tiger_list WHERE date='2025-06-25'")
print(f"龙虎榜: {cursor.fetchone()[0]}条")

cursor.execute("SELECT COUNT(*) FROM one_word_boards WHERE date='2025-06-25'")
one_word_count = cursor.fetchone()[0]
print(f"一字板统计: {one_word_count}条")

print("")
print("缺少的数据:")
if one_word_count == 0:
    print("- 一字板统计数据")
print("- 一字板详细数据")
print("- 人气排名数据")

conn.close() 