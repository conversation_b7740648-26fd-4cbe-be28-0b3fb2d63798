#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
2025年6月25日综合复盘分析
基于本周数据的深度洞察
"""

import sqlite3
from datetime import datetime, timedelta
from pathlib import Path

DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def weekly_comprehensive_analysis():
    """本周综合数据分析"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        print("📊 2025年6月25日综合复盘分析")
        print("基于本周数据的深度洞察")
        print("=" * 60)
        
        # 1. 查询本周所有日期的数据
        cursor.execute('''
            SELECT DISTINCT date 
            FROM daily_review 
            WHERE date >= '2025-06-23' AND date <= '2025-06-25'
            ORDER BY date
        ''')
        dates = [row[0] for row in cursor.fetchall()]
        print(f"📅 本周数据覆盖日期: {', '.join(dates)}")
        
        # 2. 今日市场基本面分析
        print("\n🎯 今日市场基本面分析 (2025-06-25)")
        print("-" * 40)
        
        cursor.execute('''
            SELECT market_trend, profit_effect_score, volume_billion, 
                   up_limit_count, down_limit_count, break_board_rate
            FROM daily_review 
            WHERE date='2025-06-25'
        ''')
        today_data = cursor.fetchone()
        
        if today_data:
            trend, profit_score, volume, up_count, down_count, break_rate = today_data
            print(f"  • 市场趋势: {trend}")
            print(f"  • 赚钱效应: {profit_score}分")
            print(f"  • 成交量: {volume}万亿")
            print(f"  • 涨停: {up_count}家, 跌停: {down_count}家")
            print(f"  • 破板率: {break_rate}%")
        
        # 3. 龙虎榜资金流向分析
        print("\n💰 龙虎榜资金流向分析")
        print("-" * 40)
        
        cursor.execute('''
            SELECT trader_name, stock_name, net_amount
            FROM dragon_tiger_list 
            WHERE date='2025-06-25'
            ORDER BY ABS(net_amount) DESC
        ''')
        dragon_data = cursor.fetchall()
        
        if dragon_data:
            print("  今日龙虎榜核心动向:")
            for trader, stock, net in dragon_data:
                action = "净买入" if net > 0 else "净卖出"
                print(f"    {trader}-{stock}: {action}{abs(net)}万")
        
        # 4. 空间梯队结构分析
        print("\n🪜 空间梯队结构分析")
        print("-" * 40)
        
        cursor.execute('''
            SELECT board_level, COUNT(*) as count, GROUP_CONCAT(stock_name) as stocks
            FROM space_ladder 
            WHERE date='2025-06-25' 
            AND board_level IN ('1', '2', '3', '4', '5', '6', '7')
            GROUP BY board_level 
            ORDER BY CAST(board_level AS INTEGER)
        ''')
        ladder_data = cursor.fetchall()
        
        if ladder_data:
            print("  今日梯队分布:")
            total_stocks = 0
            existing_boards = []
            for level, count, stocks in ladder_data:
                total_stocks += count
                existing_boards.append(int(level))
                stock_list = stocks.split(',')[:2] if stocks else []
                stock_display = ', '.join(stock_list)
                if count > 2:
                    stock_display += f" 等{count}只"
                print(f"    {level}板: {count}只 ({stock_display})")
            
            missing_boards = [i for i in range(1, 8) if i not in existing_boards]
            if missing_boards:
                print(f"    ⚠️ 缺失板数: {missing_boards} (真实市场情况)")
            
            print(f"  梯队总数: {total_stocks}只连板股")
        
        # 5. 一字板数据分析
        print("\n🔒 一字板数据分析")
        print("-" * 40)
        
        cursor.execute('''
            SELECT COUNT(*) as total_count
            FROM yiziban_data 
            WHERE 日期='2025-06-25'
        ''')
        one_word_count = cursor.fetchone()[0]
        
        if one_word_count > 0:
            cursor.execute('''
                SELECT 概念, COUNT(*) as count 
                FROM yiziban_data 
                WHERE 日期='2025-06-25' 
                GROUP BY 概念 
                ORDER BY count DESC
            ''')
            concept_dist = cursor.fetchall()
            
            print(f"  一字板总数: {one_word_count}只")
            print("  概念分布:")
            for concept, count in concept_dist:
                print(f"    {concept}: {count}只")
        
        # 6. 人气排名洞察
        print("\n🔥 人气排名洞察")
        print("-" * 40)
        
        cursor.execute('''
            SELECT 股票名称, 涨跌幅, 热度值, 板数信息
            FROM popularity_ranking 
            WHERE 日期='2025-06-25' AND 时间点='晚上收盘后'
            ORDER BY 排名 
            LIMIT 10
        ''')
        top_10_popularity = cursor.fetchall()
        
        if top_10_popularity:
            print("  人气前10:")
            for i, (stock, change, popularity, concept) in enumerate(top_10_popularity, 1):
                print(f"    {i}. {stock} {change} ({popularity}万热度) - {concept}")
        
        # 7. 本周数据对比分析（如果有历史数据）
        if len(dates) > 1:
            print(f"\n📈 本周趋势对比 ({dates[0]} → {dates[-1]})")
            print("-" * 40)
            
            # 成交量趋势
            cursor.execute('''
                SELECT date, volume_billion
                FROM daily_review 
                WHERE date IN ({})
                ORDER BY date
            '''.format(','.join(['?' for _ in dates])), dates)
            volume_trend = cursor.fetchall()
            
            if len(volume_trend) >= 2:
                print("  成交量趋势:")
                for date, volume in volume_trend:
                    print(f"    {date}: {volume}万亿")
        
        # 8. 核心数据洞察
        print("\n💡 核心数据洞察")
        print("-" * 40)
        
        insights = []
        
        # 市场情绪分析
        if today_data:
            if float(break_rate) < 20:
                insights.append("✅ 破板率低于20%，市场承接力较强")
            else:
                insights.append("⚠️ 破板率偏高，市场分歧较大")
            
            if int(up_count) > 60:
                insights.append("✅ 涨停数量充足，市场活跃度高")
            
            if float(volume) > 1.5:
                insights.append("✅ 成交量放大，资金参与度高")
        
        # 梯队结构分析
        if ladder_data:
            if 6 not in existing_boards:
                insights.append("⚠️ 梯队结构不完整(缺6板)，高低切换存在断档")
            
            if len(existing_boards) >= 5:
                insights.append("✅ 梯队层次丰富，有利于资金接力")
        
        # 龙虎榜分析
        if dragon_data:
            养家_action = None
            for trader, stock, net in dragon_data:
                if "炒股养家" in trader:
                    if net > 0:
                        养家_action = f"净买入{stock}{net}万"
                    else:
                        养家_action = f"净卖出{stock}{abs(net)}万"
                    break
            
            if 养家_action:
                insights.append(f"🎯 炒股养家{养家_action}，值得关注其操作方向")
        
        # 一字板分析
        if one_word_count > 0:
            if one_word_count >= 10:
                insights.append(f"✅ 一字板数量{one_word_count}只，市场情绪火热")
            
            # 分析一字板概念集中度
            if concept_dist:
                main_concept = concept_dist[0]
                if main_concept[1] >= 3:
                    insights.append(f"🔥 {main_concept[0]}成为一字板主流({main_concept[1]}只)，题材效应显著")
        
        if insights:
            for insight in insights:
                print(f"  {insight}")
        else:
            print("  暂无特殊洞察，市场处于常规状态")
        
        # 9. 明日关注要点
        print("\n🎯 明日关注要点")
        print("-" * 40)
        
        focus_points = []
        
        # 基于今日数据给出明日关注点
        if ladder_data:
            # 找出可能晋级的个股
            for level, count, stocks in ladder_data:
                if level in ['4', '5'] and count > 0:  # 4板、5板明日可能晋级
                    next_level = int(level) + 1
                    stock_names = stocks.split(',')[:2] if stocks else []
                    focus_points.append(f"关注{level}板个股({', '.join(stock_names)})能否晋级{next_level}板")
        
        if dragon_data:
            # 关注龙虎榜大额买入的个股
            for trader, stock, net in dragon_data:
                if net > 3000:  # 净买入超过3000万
                    focus_points.append(f"重点关注{stock}(今日{trader}大额买入)")
        
        if not focus_points:
            focus_points.append("关注市场整体情绪延续性")
            focus_points.append("观察成交量能否维持")
        
        for i, point in enumerate(focus_points, 1):
            print(f"  {i}. {point}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 综合分析失败: {e}")
        return False

if __name__ == "__main__":
    weekly_comprehensive_analysis() 