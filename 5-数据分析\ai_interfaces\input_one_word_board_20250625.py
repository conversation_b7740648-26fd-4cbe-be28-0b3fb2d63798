#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
录入2025年6月25日一字板数据
基于用户提供的截图
"""

import sqlite3
from datetime import datetime
from pathlib import Path

DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def input_one_word_board_data():
    """录入一字板数据"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        # 一字板统计数据
        cursor.execute('''
            INSERT INTO one_word_boards 
            (date, total_count, sector_distribution, key_stocks, market_signal, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            '2025-06-25',
            10,  # 总数10只
            '固态电池(3只)、芯片(1只)、光伏(1只)、金融概念(1只)、无人驾驶(3只)、机器人概念(1只)',
            '湘潭电化、兴业股份、新通联、吉大正元、浙江世宝、联诚精密、瑞玛精密、永安行、大东南、天际股份',
            '多题材一字板分布，市场情绪活跃',
            datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ))
        
        # 一字板详细数据
        one_word_stocks = [
            ('湘潭电化', '002125', '5连板', '固态电池'),
            ('兴业股份', '603928', '5连板', '芯片'),
            ('新通联', '603022', '4连板', '光伏'),
            ('吉大正元', '003029', '3连板', '金融概念'),
            ('浙江世宝', '002703', '2连板', '无人驾驶'),
            ('联诚精密', '002921', '2连板', '机器人概念'),
            ('瑞玛精密', '002976', '2连板', '无人驾驶'),
            ('永安行', '603776', '2连板', '无人驾驶'),
            ('大东南', '002263', '2连板', '固态电池'),
            ('天际股份', '002759', '2连板', '固态电池')
        ]
        
        for stock_name, stock_code, board_info, concept in one_word_stocks:
            cursor.execute('''
                INSERT INTO yiziban_data 
                (日期, 时间, 股票名称, 股票代码, 板数, 概念, 录入时间)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                '2025-06-25',
                '09:25-09:30',
                stock_name,
                stock_code,
                board_info,
                concept,
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))
        
        conn.commit()
        print("✅ 一字板数据录入成功")
        
        # 验证录入结果
        cursor.execute("SELECT COUNT(*) FROM one_word_boards WHERE date='2025-06-25'")
        stat_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM yiziban_data WHERE 日期='2025-06-25'")
        detail_count = cursor.fetchone()[0]
        
        print(f"📊 一字板统计: {stat_count}条")
        print(f"📋 一字板详细: {detail_count}条")
        
        # 显示录入的一字板数据
        cursor.execute("SELECT 股票名称, 板数, 概念 FROM yiziban_data WHERE 日期='2025-06-25'")
        results = cursor.fetchall()
        
        print("\n📈 一字板详细清单:")
        for stock, board, concept in results:
            print(f"  {stock} - {board} - {concept}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 一字板数据录入失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 开始录入2025年6月25日一字板数据...")
    print("📊 基于用户提供的截图数据")
    print("=" * 50)
    
    if input_one_word_board_data():
        print("\n🎉 一字板数据录入完成！")
    else:
        print("\n❌ 一字板数据录入失败！") 