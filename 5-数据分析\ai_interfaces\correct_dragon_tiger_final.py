#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
基于截图重新修正上塘路龙虎榜数据
"""

import sqlite3
from datetime import datetime
from pathlib import Path

DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def correct_shangtang_data():
    """基于截图修正上塘路数据"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        # 删除错误数据
        cursor.execute("DELETE FROM dragon_tiger_list WHERE date='2025-06-25'")
        print("🗑️ 已删除错误数据")
        
        # 基于截图的正确数据
        correct_data = [
            # 炒股养家 - 湘潭电化 (买入5679万，卖出98.33万，净买入5580.67万)
            ('2025-06-25', '炒股养家', '002125', '湘潭电化', 5679, 98.33, 5580.67, '净买入', '5连板继续加仓'),
            
            # 上塘路 - 柏诚股份 (买入1427万，卖出0万，净买入1427万)
            ('2025-06-25', '上塘路', '603303', '柏诚股份', 1427, 0, 1427, '净买入', '首板买入'),
            
            # 上塘路 - 湘潭电化 (买入0万，卖出2942万，净卖出2942万)  
            ('2025-06-25', '上塘路', '002125', '湘潭电化', 0, 2942, -2942, '净卖出', '5连板获利了结'),
        ]
        
        for data in correct_data:
            cursor.execute('''
                INSERT INTO dragon_tiger_list 
                (date, trader_name, stock_code, stock_name, buy_amount, sell_amount, net_amount, operation_type, market_signal, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (*data, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        conn.commit()
        print("✅ 龙虎榜数据基于截图修正完成")
        
        # 验证结果
        cursor.execute("SELECT trader_name, stock_name, buy_amount, sell_amount, net_amount, operation_type FROM dragon_tiger_list WHERE date='2025-06-25' ORDER BY trader_name, stock_name")
        results = cursor.fetchall()
        
        print("\n📊 基于截图修正后的龙虎榜数据:")
        for row in results:
            trader, stock, buy, sell, net, op_type = row
            print(f"  {trader}: {stock}")
            print(f"    买入: {buy}万, 卖出: {sell}万")
            print(f"    净额: {net}万 ({op_type})")
            print("-" * 30)
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修正失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 基于截图重新修正2025年6月25日龙虎榜数据...")
    print("📊 修正上塘路的买卖数据")
    print("=" * 50)
    
    if correct_shangtang_data():
        print("\n🎉 龙虎榜数据基于截图修正完成！")
    else:
        print("\n❌ 龙虎榜数据修正失败！") 