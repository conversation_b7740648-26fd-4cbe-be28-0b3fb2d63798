#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
简化版数据验证脚本
"""

import sqlite3
from pathlib import Path

# 数据库路径
DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def simple_verify():
    """简化验证数据录入结果"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        print("📊 2025年6月25日数据录入验证结果")
        print("=" * 40)
        
        # 检查各表数据量
        tables = [
            ('daily_review', '日度复盘'),
            ('space_ladder', '空间梯队'),
            ('sector_analysis', '板块分析'),
            ('dragon_tiger_list', '龙虎榜'),
            ('distinctive_stocks', '辨识度个股')
        ]
        
        total_records = 0
        for table_name, chinese_name in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE date='2025-06-25'")
            count = cursor.fetchone()[0]
            print(f"✅ {chinese_name}: {count}条记录")
            total_records += count
        
        print(f"\n📈 总计录入: {total_records}条记录")
        
        # 显示关键数据
        cursor.execute("SELECT index_close, comprehensive_strength, max_board_count FROM daily_review WHERE date='2025-06-25'")
        daily = cursor.fetchone()
        if daily:
            print(f"🎯 关键指标: 指数{daily[0]}, 强度{daily[1]}, 最高{daily[2]}板")
        
        conn.close()
        print("\n✅ 数据录入成功完成！")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    simple_verify() 