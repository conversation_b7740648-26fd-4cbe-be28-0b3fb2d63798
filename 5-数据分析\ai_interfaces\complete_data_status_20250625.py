#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
2025年6月25日完整数据状态检查
显示所有已录入的数据和梯队结构分析
"""

import sqlite3
from pathlib import Path

DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def check_complete_data_status():
    """检查完整数据状态"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        print("📊 2025年6月25日数据录入状态")
        print("=" * 50)
        
        # 1. 日度复盘数据
        cursor.execute("SELECT COUNT(*) FROM daily_review WHERE date='2025-06-25'")
        daily_count = cursor.fetchone()[0]
        print(f"📈 日度复盘: {daily_count}条")
        
        # 2. 空间梯队数据
        cursor.execute("SELECT COUNT(*) FROM space_ladder WHERE date='2025-06-25'")
        ladder_count = cursor.fetchone()[0]
        print(f"🪜 空间梯队: {ladder_count}条")
        
        # 显示梯队结构
        cursor.execute('''
            SELECT board_level, COUNT(*) as count, GROUP_CONCAT(stock_name) as stocks
            FROM space_ladder 
            WHERE date='2025-06-25' 
            GROUP BY board_level 
            ORDER BY board_level
        ''')
        ladder_data = cursor.fetchall()
        
        print("\n  梯队详细结构:")
        for level, count, stocks in ladder_data:
            if stocks:
                stock_list = stocks.split(',')[:3]  # 只显示前3只
                stock_display = ', '.join(stock_list)
                if count > 3:
                    stock_display += f" 等{count}只"
                print(f"    {level}: {count}只 ({stock_display})")
            else:
                print(f"    {level}: {count}只")
        
        # 3. 龙虎榜数据
        cursor.execute("SELECT COUNT(*) FROM dragon_tiger_list WHERE date='2025-06-25'")
        dragon_count = cursor.fetchone()[0]
        print(f"\n🐉 龙虎榜: {dragon_count}条")
        
        if dragon_count > 0:
            cursor.execute('''
                SELECT trader_name, stock_name, buy_amount, sell_amount, net_amount
                FROM dragon_tiger_list 
                WHERE date='2025-06-25'
                ORDER BY ABS(net_amount) DESC
            ''')
            dragon_data = cursor.fetchall()
            print("  龙虎榜详情:")
            for trader, stock, buy, sell, net in dragon_data:
                print(f"    {trader}-{stock}: 净{net}万")
        
        # 4. 板块分析数据
        cursor.execute("SELECT COUNT(*) FROM sector_analysis WHERE date='2025-06-25'")
        sector_count = cursor.fetchone()[0]
        print(f"\n📊 板块分析: {sector_count}条")
        
        # 5. 辨识度个股数据
        cursor.execute("SELECT COUNT(*) FROM distinctive_stocks WHERE date='2025-06-25'")
        distinctive_count = cursor.fetchone()[0]
        print(f"⭐ 辨识度个股: {distinctive_count}条")
        
        # 6. 一字板数据
        cursor.execute("SELECT COUNT(*) FROM one_word_boards WHERE date='2025-06-25'")
        one_word_stat_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM yiziban_data WHERE 日期='2025-06-25'")
        one_word_detail_count = cursor.fetchone()[0]
        print(f"🔒 一字板统计: {one_word_stat_count}条")
        print(f"🔒 一字板详细: {one_word_detail_count}条")
        
        if one_word_detail_count > 0:
            cursor.execute("SELECT 股票名称, 板数, 概念 FROM yiziban_data WHERE 日期='2025-06-25' ORDER BY 板数 DESC")
            one_word_data = cursor.fetchall()
            print("  一字板清单:")
            for stock, board, concept in one_word_data:
                print(f"    {stock} {board} ({concept})")
        
        # 7. 人气排名数据
        cursor.execute("SELECT COUNT(*) FROM popularity_ranking WHERE date='2025-06-25'")
        popularity_count = cursor.fetchone()[0]
        print(f"\n🔥 人气排名: {popularity_count}条")
        
        # 总结
        total_records = (daily_count + ladder_count + dragon_count + 
                        sector_count + distinctive_count + 
                        one_word_stat_count + one_word_detail_count + 
                        popularity_count)
        
        print(f"\n📋 总计录入: {total_records}条记录")
        
        # 数据完整性评估
        print("\n✅ 数据完整性评估:")
        completeness_items = [
            ("日度复盘", daily_count > 0),
            ("空间梯队", ladder_count > 0),
            ("龙虎榜", dragon_count > 0),
            ("板块分析", sector_count > 0),
            ("辨识度个股", distinctive_count > 0),
            ("一字板数据", one_word_detail_count > 0),
        ]
        
        for item_name, is_complete in completeness_items:
            status = "✅" if is_complete else "❌"
            print(f"  {status} {item_name}")
        
        missing_items = [item for item, complete in completeness_items if not complete]
        if missing_items:
            print(f"\n⚠️ 缺少数据: {', '.join([item for item, _ in missing_items])}")
        else:
            print("\n🎉 数据录入完整！")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据状态检查失败: {e}")
        return False

if __name__ == "__main__":
    check_complete_data_status() 