#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
检查2025年6月25日复盘数据完整性
"""

import sqlite3
from pathlib import Path

DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def check_data_completeness():
    """检查数据完整性"""
    conn = sqlite3.connect(str(DB_PATH))
    cursor = conn.cursor()
    
    print("📊 2025年6月25日复盘数据完整性检查")
    print("=" * 50)
    
    # 检查各表数据
    tables_to_check = [
        ('daily_review', '日度复盘'),
        ('space_ladder', '空间梯队'),
        ('sector_analysis', '板块分析'),
        ('dragon_tiger_list', '龙虎榜'),
        ('distinctive_stocks', '辨识度个股'),
        ('one_word_boards', '一字板统计'),
        ('popularity_ranking', '人气排名'),
        ('yiziban_data', '一字板详细')
    ]
    
    for table, name in tables_to_check:
        cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE date='2025-06-25' OR 日期='2025-06-25'")
        count = cursor.fetchone()[0]
        status = "✅" if count > 0 else "❌"
        print(f"{status} {name}: {count}条记录")
    
    print("\n📋 缺少的数据类型:")
    
    # 检查一字板数据
    cursor.execute("SELECT COUNT(*) FROM one_word_boards WHERE date='2025-06-25'")
    one_word_count = cursor.fetchone()[0]
    if one_word_count == 0:
        print("❌ 一字板统计数据 - 需要今日一字板总数和分布")
    
    # 检查详细一字板数据
    cursor.execute("SELECT COUNT(*) FROM yiziban_data WHERE 日期='2025-06-25'")
    yiziban_detail_count = cursor.fetchone()[0]
    if yiziban_detail_count == 0:
        print("❌ 一字板详细数据 - 需要具体一字板股票清单")
    
    # 检查人气排名数据
    cursor.execute("SELECT COUNT(*) FROM popularity_ranking WHERE 日期='2025-06-25'")
    popularity_count = cursor.fetchone()[0]
    if popularity_count == 0:
        print("❌ 人气排名数据 - 需要今日人气股排名")
    
    print("\n📈 已录入数据概览:")
    
    # 显示已有数据概览
    cursor.execute("SELECT index_close, comprehensive_strength, max_board_count FROM daily_review WHERE date='2025-06-25'")
    daily_result = cursor.fetchone()
    if daily_result:
        print(f"✅ 指数收盘: {daily_result[0]}, 综合强度: {daily_result[1]}, 最高板: {daily_result[2]}板")
    
    cursor.execute("SELECT COUNT(*) FROM space_ladder WHERE date='2025-06-25'")
    ladder_count = cursor.fetchone()[0]
    print(f"✅ 空间梯队: {ladder_count}只个股")
    
    cursor.execute("SELECT COUNT(*) FROM dragon_tiger_list WHERE date='2025-06-25'")
    dragon_count = cursor.fetchone()[0]
    print(f"✅ 龙虎榜: {dragon_count}条交易记录")
    
    conn.close()

if __name__ == "__main__":
    check_data_completeness() 