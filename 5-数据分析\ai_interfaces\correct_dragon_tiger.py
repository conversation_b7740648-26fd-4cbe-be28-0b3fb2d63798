#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
重新修正龙虎榜数据 - 基于截图准确录入
"""

import sqlite3
from datetime import datetime
from pathlib import Path

DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def correct_dragon_tiger_data():
    """重新修正龙虎榜数据"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        # 删除错误数据
        cursor.execute("DELETE FROM dragon_tiger_list WHERE date='2025-06-25'")
        print("🗑️ 已删除错误数据")
        
        # 基于截图的正确数据
        # 从截图看：
        # 炒股养家: 湘潭电化 买5679万 卖9833万 (红色=买入，绿色=卖出)
        # 上塘路: 湘潭电化 买1427万 卖2942万
        # 上塘路: 怕诚股份 买1427万 卖2942万 (这个数据看起来重复了，但截图显示确实是这样)
        
        correct_data = [
            # 炒股养家 - 湘潭电化 (买入5679万，卖出9833万，净卖出4154万)
            ('2025-06-25', '炒股养家', '002125', '湘潭电化', 5679, 9833, -4154, '净卖出', '5连板高位减仓'),
            
            # 上塘路 - 湘潭电化 (买入1427万，卖出2942万，净卖出1515万)  
            ('2025-06-25', '上塘路', '002125', '湘潭电化', 1427, 2942, -1515, '净卖出', '5连板获利了结'),
            
            # 上塘路 - 怕诚股份 (买入1427万，卖出2942万，净卖出1515万)
            # 注意：从截图看确实是相同数值，可能是同一笔交易或者显示问题
            ('2025-06-25', '上塘路', '601133', '怕诚股份', 1427, 2942, -1515, '净卖出', '首板参与后离场'),
        ]
        
        for data in correct_data:
            cursor.execute('''
                INSERT INTO dragon_tiger_list 
                (date, trader_name, stock_code, stock_name, buy_amount, sell_amount, net_amount, operation_type, market_signal, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (*data, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        conn.commit()
        print("✅ 龙虎榜数据重新录入完成")
        
        # 验证结果
        cursor.execute("SELECT trader_name, stock_name, buy_amount, sell_amount, net_amount FROM dragon_tiger_list WHERE date='2025-06-25'")
        results = cursor.fetchall()
        
        print("\n📊 最终龙虎榜数据:")
        for row in results:
            trader, stock, buy, sell, net = row
            print(f"  {trader}: {stock} 买入{buy}万 卖出{sell}万 净额{net}万")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修正失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 重新修正2025年6月25日龙虎榜数据...")
    print("📊 基于用户提供的截图进行准确录入")
    print("=" * 50)
    
    if correct_dragon_tiger_data():
        print("\n🎉 龙虎榜数据修正完成！")
    else:
        print("\n❌ 龙虎榜数据修正失败！") 