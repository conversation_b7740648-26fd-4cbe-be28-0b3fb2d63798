#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
修正2025年6月25日龙虎榜数据
基于用户提供的正确截图数据
"""

import sqlite3
from datetime import datetime
from pathlib import Path

# 数据库路径
DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def check_current_data():
    """检查当前龙虎榜数据"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM dragon_tiger_list WHERE date='2025-06-25'")
        results = cursor.fetchall()
        
        print("🔍 当前龙虎榜数据:")
        for row in results:
            print(f"  {row}")
        
        conn.close()
        return results
        
    except Exception as e:
        print(f"❌ 查看数据失败: {e}")
        return []

def fix_dragon_tiger_data():
    """修正龙虎榜数据"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        # 先删除错误数据
        cursor.execute("DELETE FROM dragon_tiger_list WHERE date='2025-06-25'")
        print("🗑️ 已删除错误的龙虎榜数据")
        
        # 根据截图录入正确数据
        correct_data = [
            # 炒股养家 - 湘潭电化
            ('2025-06-25', '炒股养家', '002125', '湘潭电化', 5679, 9833, -4154, '净卖出', '5连板高位减仓'),
            
            # 上塘路 - 湘潭电化  
            ('2025-06-25', '上塘路', '002125', '湘潭电化', 1427, 2942, -1515, '净卖出', '5连板获利了结'),
            
            # 上塘路 - 怕诚股份
            ('2025-06-25', '上塘路', '601133', '怕诚股份', 1427, 2942, -1515, '净卖出', '首板参与后离场'),
        ]
        
        for data in correct_data:
            cursor.execute('''
                INSERT INTO dragon_tiger_list 
                (date, trader_name, stock_code, stock_name, buy_amount, sell_amount, net_amount, operation_type, market_signal, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (*data, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        conn.commit()
        print("✅ 龙虎榜数据修正完成")
        
        # 验证修正结果
        cursor.execute("SELECT trader_name, stock_name, buy_amount, sell_amount, net_amount FROM dragon_tiger_list WHERE date='2025-06-25'")
        results = cursor.fetchall()
        
        print("\n📊 修正后的龙虎榜数据:")
        for row in results:
            trader, stock, buy, sell, net = row
            print(f"  {trader}: {stock} 买入{buy}万 卖出{sell}万 净额{net}万")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修正数据失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始修正2025年6月25日龙虎榜数据...")
    print("=" * 50)
    
    # 检查当前数据
    current_data = check_current_data()
    
    if current_data:
        print(f"\n发现 {len(current_data)} 条现有数据，准备修正...")
        
        # 修正数据
        if fix_dragon_tiger_data():
            print("\n🎉 龙虎榜数据修正成功！")
        else:
            print("\n❌ 龙虎榜数据修正失败！")
    else:
        print("\n⚠️ 未发现现有数据")

if __name__ == "__main__":
    main() 