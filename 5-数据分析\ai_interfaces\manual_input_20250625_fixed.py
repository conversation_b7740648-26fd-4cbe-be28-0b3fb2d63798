#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-
"""
2025年6月25日复盘数据手动录入脚本 - 修正版
基于现有数据库表结构进行数据录入
"""

import sys
import sqlite3
from datetime import datetime
from pathlib import Path

# 数据库路径
DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

def create_connection():
    """创建数据库连接"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        return conn
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def insert_daily_review():
    """录入日度复盘数据"""
    conn = create_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # 使用现有表结构的字段名
        daily_data = {
            'date': '2025-06-25',
            'index_close': 3455.97,  # 沪指收盘
            'index_change': 35.41,  # 涨跌点数
            'index_change_pct': 1.04,  # 涨跌幅%
            'volume_billion': 1602.7,  # 成交量(十亿)
            'limit_up_count': 66,  # 涨停家数
            'limit_down_count': 2,  # 跌停家数
            'comprehensive_strength': 75,  # 综合强度
            'broken_board_rate': 18.52,  # 破板率%
            'two_board_rate': 50,  # 二板连板率50%
            'three_board_rate': 63,  # 三板连板率63%
            'max_board_count': 7,  # 最高板数(诺德股份7板)
            'pressure_level': 3461.5,  # 压力位
            'kdj_status': '金手指区间，指数走出三连阳',
            'institutional_direction': '机构交易35笔，净买入20笔，3笔过亿买入',
            'review_view': '市场保持走强，大金融放量走强，带动指数向上突破。权重大金融开始带动走强，市场有机会从横盘震荡转为向上的趋势',
            'trend_status': '指数价涨量增，量价正常，对后市仍可保持信心',
            'ladder_structure': '题材存在炒作机会，一板45家，二板13家，三板3家，高度板5家',
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 插入日度复盘数据
        columns = ', '.join(daily_data.keys())
        placeholders = ', '.join(['?' for _ in daily_data])
        
        cursor.execute(f'''
            INSERT OR REPLACE INTO daily_review ({columns})
            VALUES ({placeholders})
        ''', list(daily_data.values()))
        
        print(f"✅ 日度复盘数据录入成功: {daily_data['date']}")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ 日度复盘数据录入失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def insert_space_ladder():
    """录入空间梯队数据"""
    conn = create_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # 空间梯队数据(基于截图中的市场梯队信息)
        ladder_data = [
            # 七板
            ('2025-06-25', 7, '600110', '诺德股份', '固态电池', True, 5, None),
            
            # 五板  
            ('2025-06-25', 5, '002125', '湘潭电化', '固态电池', True, 5, None),
            ('2025-06-25', 5, '603928', '兴业股份', '芯片', True, 4, None),
            
            # 四板
            ('2025-06-25', 4, '002846', '英联股份', '光伏', False, 4, None),
            ('2025-06-25', 4, '603022', '新通联', '光伏', False, 4, None),
            
            # 三板
            ('2025-06-25', 3, '003029', '吉大正元', '金融概念', False, 4, None),
            ('2025-06-25', 3, '002537', '海联金汇', '金融概念', False, 4, None),
            ('2025-06-25', 3, '300801', '泰和科技', '芯片', False, 4, None),
            
            # 二板
            ('2025-06-25', 2, '002759', '天际股份', '固态电池', False, 3, None),
            ('2025-06-25', 2, '002263', '大东南', '固态电池', False, 3, None),
            ('2025-06-25', 2, '603906', '龙蟠科技', '固态电池', False, 3, None),
            
            # 首板
            ('2025-06-25', 1, '002189', '中光学', '军工', False, 3, None),
            ('2025-06-25', 1, '600435', '北方导航', '军工', False, 3, None),
            ('2025-06-25', 1, '603948', '建设工业', '军工', False, 3, None),
        ]
        
        for date, board_level, stock_code, stock_name, concept, is_one_word, strength, ladder_pos in ladder_data:
            cursor.execute('''
                INSERT OR REPLACE INTO space_ladder 
                (date, board_level, stock_code, stock_name, concept, is_one_word_board, strength_rating, ladder_position, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (date, board_level, stock_code, stock_name, concept, is_one_word, strength, ladder_pos, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        print("✅ 空间梯队数据录入成功")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ 空间梯队数据录入失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def insert_sector_analysis():
    """录入板块分析数据"""
    conn = create_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # 板块分析数据(使用现有表结构)
        sector_data = [
            # 强势板块
            ('2025-06-25', '兵装重组概念', 5, '华强科技', '军工重组概念活跃', '重组预期推动', 1),
            ('2025-06-25', '多元金融', 8, '东方财富,天利科技', '大金融板块走强', '权重带动指数上涨', 2),
            ('2025-06-25', '军工装备', 12, '中光学,北方导航,建设工业', '军工板块全面爆发', '政策预期+资金流入', 3),
            ('2025-06-25', '固态电池', 15, '诺德股份,湘潭电化,兴业股份', '固态电池持续强势', '技术突破+产业化进程', 4),
            ('2025-06-25', '金融概念', 6, '吉大正元,海联金汇', '金融科技概念活跃', '数字化转型', 5),
        ]
        
        for date, sector_name, limit_up_count, stocks, characteristics, logic, rank in sector_data:
            cursor.execute('''
                INSERT OR REPLACE INTO sector_analysis 
                (date, sector_name, limit_up_count, representative_stocks, sector_characteristics, logic_analysis, strength_rank, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (date, sector_name, limit_up_count, stocks, characteristics, logic, rank, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        print("✅ 板块分析数据录入成功")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ 板块分析数据录入失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def insert_dragon_tiger_list():
    """录入龙虎榜数据"""
    conn = create_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # 龙虎榜数据(基于截图信息，使用现有表结构)
        dragon_tiger_data = [
            ('2025-06-25', '炒股养家', '002125', '湘潭电化', 5679, -9833, -4154, '卖出', '5连板资金分歧'),
            ('2025-06-25', '上塘路', '002125', '湘潭电化', 1427, -2942, -1515, '卖出', '高位获利了结'),
            ('2025-06-25', '上塘路', '601133', '怕诚股份', 1427, -2942, -1515, '卖出', '首板参与'),
        ]
        
        for date, trader_name, stock_code, stock_name, buy_amount, sell_amount, net_amount, operation_type, market_signal in dragon_tiger_data:
            cursor.execute('''
                INSERT OR REPLACE INTO dragon_tiger_list 
                (date, trader_name, stock_code, stock_name, buy_amount, sell_amount, net_amount, operation_type, market_signal, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (date, trader_name, stock_code, stock_name, buy_amount, sell_amount, net_amount, operation_type, market_signal, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        print("✅ 龙虎榜数据录入成功")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ 龙虎榜数据录入失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def insert_distinctive_stocks():
    """录入辨识度个股数据"""
    conn = create_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # 辨识度个股数据
        distinctive_data = [
            ('2025-06-25', '600110', '诺德股份', 7, '固态电池', '7板龙头，固态电池概念领涨', 5, 4, '中'),
            ('2025-06-25', '002125', '湘潭电化', 5, '固态电池', '5连板，炒股养家减仓', 4, 3, '高'),
            ('2025-06-25', '603928', '兴业股份', 5, '芯片', '5连板，芯片概念强势', 4, 4, '中'),
            ('2025-06-25', '601606', '长城军工', 1, '军工', '6天5板反包，军工龙头', 4, 4, '中'),
            ('2025-06-25', '300591', '万里马', 1, '军工', '3天2板反包，军工概念', 3, 3, '中'),
        ]
        
        for date, stock_code, stock_name, board_level, concept, features, strength, value, risk in distinctive_data:
            cursor.execute('''
                INSERT OR REPLACE INTO distinctive_stocks 
                (date, stock_code, stock_name, board_level, concept, distinctive_features, strength_rating, operation_value, risk_level, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (date, stock_code, stock_name, board_level, concept, features, strength, value, risk, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        print("✅ 辨识度个股数据录入成功")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ 辨识度个股数据录入失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def main():
    """主执行函数"""
    print("🚀 开始录入2025年6月25日复盘数据...")
    
    success_count = 0
    
    # 录入各类数据
    if insert_daily_review():
        success_count += 1
    
    if insert_space_ladder():
        success_count += 1
        
    if insert_sector_analysis():
        success_count += 1
        
    if insert_dragon_tiger_list():
        success_count += 1
        
    if insert_distinctive_stocks():
        success_count += 1
    
    print(f"\n📊 数据录入完成！成功录入 {success_count}/5 个数据模块")
    print("✅ 2025年6月25日复盘数据已完整录入到本地数据库")
    print(f"📈 录入数据包括：日度复盘、空间梯队({len([])})、板块分析、龙虎榜、辨识度个股")

if __name__ == "__main__":
    main() 