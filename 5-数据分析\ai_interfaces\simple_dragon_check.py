#!/usr/bin/env python3
# -*- coding: utf-8-sig -*-

import sqlite3
from pathlib import Path

DB_PATH = Path(__file__).parent.parent / "databases" / "trading_data.db"

conn = sqlite3.connect(str(DB_PATH))
cursor = conn.cursor()

print("龙虎榜数据检查:")
cursor.execute("SELECT trader_name, stock_name, buy_amount, sell_amount, net_amount FROM dragon_tiger_list WHERE date='2025-06-25'")
results = cursor.fetchall()

for row in results:
    trader, stock, buy, sell, net = row
    print(f"{trader}: {stock} 买{buy}万 卖{sell}万 净{net}万")

conn.close() 